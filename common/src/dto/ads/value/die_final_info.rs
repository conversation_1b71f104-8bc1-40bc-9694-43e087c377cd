use serde::{Deserialize, Serialize};
use std::sync::Arc;

/// DieFinalInfo structure for die-level final test information
/// Used to override IS_FINAL_TEST_IGNORE_TP based on ECID and C_PART_ID matching
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct DieFinalInfo {
    pub customer: Arc<str>,
    pub factory: Arc<str>,
    pub test_area: Arc<str>,
    pub device_id: Arc<str>,
    pub lot_type: Arc<str>,
    pub test_stage: Arc<str>,
    pub lot_id: Arc<str>,
    pub wafer_no: Arc<str>,
    pub ecid: Arc<str>,
    pub c_part_id: Option<u32>,
    pub is_final_test_ignore_tp: Option<u8>,
}
