use crate::utils::decimal::Decimal38_18;
use chrono::{DateTime, Utc};
use clickhouse::Row;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;

/// ADS TestItemSite aggregation result structure
/// Contains statistical analysis aggregated at the site level
/// Corresponds to ads_yms_stage_test_item_program_site_cluster table
#[derive(Debug, <PERSON>lone, Serialize, Deserialize, Row, PartialEq)]
#[allow(non_snake_case)]
pub struct TestItemSite {
    // Data source and upload information
    pub DATA_SOURCE: Arc<str>,
    pub UPLOAD_TYPE: Arc<str>,
    pub CUSTOMER: Arc<str>,
    pub SUB_CUSTOMER: Arc<str>,

    // Factory and location information
    pub FAB: Arc<str>,
    pub FAB_SITE: Arc<str>,
    pub FACTORY: Arc<str>,
    pub FACTORY_SITE: Arc<str>,
    pub TEST_AREA: Arc<str>,
    pub TEST_STAGE: Arc<str>,

    // Product and lot information
    pub DEVICE_ID: Arc<str>,
    pub LOT_TYPE: Arc<str>,
    pub LOT_ID: Arc<str>,
    pub SBLOT_ID: Arc<str>,
    pub WAFER_ID: Arc<str>,
    pub WAFER_NO: Arc<str>,
    pub WAFER_LOT_ID: Arc<str>,
    pub PRODUCT: Arc<str>,
    pub PRODUCT_TYPE: Arc<str>,
    pub PRODUCT_FAMILY: Arc<str>,

    // Test program information
    pub TEST_PROGRAM: Arc<str>,
    pub TEST_PROGRAM_VERSION: Arc<str>,
    pub TEST_NUM: Option<u32>,
    pub TEST_TXT: Arc<str>,
    pub TEST_ITEM: Arc<str>,

    // Site information
    pub SITE: Option<u32>,

    // Processing flags
    pub IS_PASS_ONLY: u8,
    pub IS_FINAL: u8,

    // List fields for aggregated data
    pub UNITS_LIST: Arc<str>,
    pub ORIGIN_UNITS_LIST: Arc<str>,
    pub LO_LIMIT_LIST: Arc<str>,
    pub HI_LIMIT_LIST: Arc<str>,
    pub ORIGIN_LO_LIMIT_LIST: Arc<str>,
    pub ORIGIN_HI_LIMIT_LIST: Arc<str>,
    pub PROCESS_LIST: Arc<str>,
    pub TESTITEM_TYPE_LIST: Arc<str>,
    pub TEST_TEMPERATURE_LIST: Arc<str>,
    pub TESTER_NAME_LIST: Arc<str>,
    pub TESTER_TYPE_LIST: Arc<str>,
    pub PROBER_HANDLER_TYP_LIST: Arc<str>,
    pub PROBER_HANDLER_ID_LIST: Arc<str>,
    pub PROBECARD_LOADBOARD_TYP_LIST: Arc<str>,
    pub PROBECARD_LOADBOARD_ID_LIST: Arc<str>,
    pub SITE_CNT_LIST: Arc<str>,
    pub SITE_NUMS_LIST: Arc<str>,

    // Count fields
    pub INPUT_CNT: i32,
    pub PASS_CNT: i32,
    pub FAIL_CNT: i32,
    pub PASSBIN_FAILINGITEM_CNT: i32,
    pub EXE_INPUT_CNT: i32,
    pub EXE_PASS_CNT: i32,
    pub EXE_FAIL_CNT: i32,

    // Statistical metrics - basic
    pub MEDIAN: Option<Decimal38_18>,
    pub MEAN: Option<Decimal38_18>,
    pub MAX: Option<Decimal38_18>,
    pub MIN: Option<Decimal38_18>,
    pub MAX_WO_OUTLIERS: Option<Decimal38_18>,
    pub MIN_WO_OUTLIERS: Option<Decimal38_18>,
    pub SUM_SQ: Option<Decimal38_18>,
    pub SUM_VALUE: Option<Decimal38_18>,
    pub STDEV_P: Option<Decimal38_18>,
    pub STDEV_S: Option<Decimal38_18>,
    pub RANGE: Option<Decimal38_18>,
    pub IQR: Option<Decimal38_18>,

    // Quantiles
    pub Q1: Option<Decimal38_18>,
    pub Q3: Option<Decimal38_18>,
    pub LOWER: Option<Decimal38_18>,
    pub UPPER: Option<Decimal38_18>,
    pub OUTLIER_CNT: u32,
    pub P1: Option<Decimal38_18>,
    pub P5: Option<Decimal38_18>,
    pub P10: Option<Decimal38_18>,
    pub P90: Option<Decimal38_18>,
    pub P95: Option<Decimal38_18>,
    pub P99: Option<Decimal38_18>,

    // Histogram data
    pub GROUP_DETAIL: HashMap<String, u32>,

    // Process capability indices
    pub PP: Option<Decimal38_18>,
    pub PPU: Option<Decimal38_18>,
    pub PPL: Option<Decimal38_18>,
    pub PPK: Option<Decimal38_18>,
    pub CP: Option<Decimal38_18>,
    pub CPU: Option<Decimal38_18>,
    pub CPL: Option<Decimal38_18>,
    pub CPK: Option<Decimal38_18>,
    pub CA: Option<Decimal38_18>,

    // Advanced statistical metrics
    pub SKEWNESS: Option<Decimal38_18>,
    pub KURTOSIS: Option<Decimal38_18>,

    // Normalization metrics
    pub NORMALIZATION_MEDIAN: Option<Decimal38_18>,
    pub NORMALIZATION_MEAN: Option<Decimal38_18>,
    pub NORMALIZATION_MAX: Option<Decimal38_18>,
    pub NORMALIZATION_MIN: Option<Decimal38_18>,
    pub NORMALIZATION_MAX_WO_OUTLIERS: Option<Decimal38_18>,
    pub NORMALIZATION_MIN_WO_OUTLIERS: Option<Decimal38_18>,
    pub NORMALIZATION_IQR: Option<Decimal38_18>,
    pub NORMALIZATION_Q1: Option<Decimal38_18>,
    pub NORMALIZATION_Q3: Option<Decimal38_18>,
    pub NORMALIZATION_LOWER: Option<Decimal38_18>,
    pub NORMALIZATION_UPPER: Option<Decimal38_18>,

    // Timestamps
    #[serde(with = "clickhouse::serde::chrono::datetime::option")]
    pub START_TIME: Option<DateTime<Utc>>,
    pub START_HOUR_KEY: Arc<str>,
    pub START_DAY_KEY: Arc<str>,
    #[serde(with = "clickhouse::serde::chrono::datetime::option")]
    pub END_TIME: Option<DateTime<Utc>>,
    pub END_HOUR_KEY: Arc<str>,
    pub END_DAY_KEY: Arc<str>,

    // System fields
    #[serde(with = "clickhouse::serde::chrono::datetime")]
    pub CREATE_TIME: DateTime<Utc>,
    pub CREATE_HOUR_KEY: Arc<str>,
    pub CREATE_DAY_KEY: Arc<str>,
    pub CREATE_USER: Arc<str>,
    #[serde(with = "clickhouse::serde::chrono::datetime")]
    pub UPLOAD_TIME: DateTime<Utc>,
    pub VERSION: i64,
    pub IS_DELETE: u8,
}

impl TestItemSite {
    /// Creates a new TestItemSite from a list of TestItemDetail items
    /// This is the equivalent of the Scala adsTestItemSite method
    pub fn from_test_items(
        items: &[crate::dto::ads::value::test_item_detail::TestItemDetail],
        is_pass_only: u8,
        product_list: &[crate::dto::ods::product_config::OdsProductConfig],
    ) -> Self {
        use crate::model::constant::{CP_MAP, EMPTY, INKLESS_MAP, M, P, PF_PASS, TEST_RAW_DATA};
        use crate::utils::date::{get_day, get_day_hour};
        use std::collections::HashSet;

        if items.is_empty() {
            return Self::default();
        }

        let item = &items[0];
        let testitem_type = item.TESTITEM_TYPE.as_ref();

        // Filter test values for statistical calculation
        let test_values: Vec<f64> = items
            .iter()
            .filter(|t| t.TEST_VALUE.is_some() && t.TEST_RESULT.is_some() && t.TEST_RESULT.unwrap() < 2)
            .map(|t| {
                let value = t.TEST_VALUE.unwrap();
                value
            })
            .collect();

        // Get product information
        let (product, product_type, product_family) = if !product_list.is_empty() {
            let product_info = &product_list[0];
            (
                if product_info.PRODUCT.is_empty() { EMPTY } else { product_info.PRODUCT.as_ref() },
                if product_info.PRODUCT_TYPE.is_empty() { EMPTY } else { product_info.PRODUCT_TYPE.as_ref() },
                if product_info.PRODUCT_FAMILY.is_empty() { EMPTY } else { product_info.PRODUCT_FAMILY.as_ref() },
            )
        } else {
            (EMPTY, EMPTY, EMPTY)
        };

        let now = chrono::Utc::now();
        let create_hour_key = get_day_hour(now);
        let create_day_key = get_day(now);

        // Create distinct string lists (equivalent to DwsCommonUtil.mkStringDistinct)
        let mk_string_distinct = |values: Vec<&str>| -> String {
            let unique: HashSet<&str> = values.into_iter().collect();
            unique.into_iter().collect::<Vec<_>>().join(",")
        };

        // Build distinct lists
        let sblot_id_arr = mk_string_distinct(items.iter().map(|i| i.SBLOT_ID.as_ref()).collect());
        let wafer_no_arr = mk_string_distinct(items.iter().map(|i| i.WAFER_NO.as_ref()).collect());
        let wafer_id_arr = mk_string_distinct(items.iter().map(|i| i.WAFER_ID.as_ref()).collect());
        let wafer_lot_id_arr = mk_string_distinct(items.iter().map(|i| i.WAFER_LOT_ID.as_ref()).collect());
        let units_arr = mk_string_distinct(items.iter().map(|i| i.UNITS.as_ref()).collect());
        let origin_units_arr = mk_string_distinct(items.iter().map(|i| i.ORIGIN_UNITS.as_ref()).collect());

        let lo_limit_strings: Vec<String> = items
            .iter()
            .filter_map(|i| i.LO_LIMIT.as_ref().map(|l| l.to_string()))
            .collect();
        let lo_limit_arr = mk_string_distinct(lo_limit_strings.iter().map(|s| s.as_str()).collect());

        let hi_limit_strings: Vec<String> = items
            .iter()
            .filter_map(|i| i.HI_LIMIT.as_ref().map(|l| l.to_string()))
            .collect();
        let hi_limit_arr = mk_string_distinct(hi_limit_strings.iter().map(|s| s.as_str()).collect());

        let origin_lo_limit_strings: Vec<String> = items
            .iter()
            .filter_map(|i| i.ORIGIN_LO_LIMIT.as_ref().map(|l| l.to_string()))
            .collect();
        let origin_lo_limit_arr = mk_string_distinct(origin_lo_limit_strings.iter().map(|s| s.as_str()).collect());

        let origin_hi_limit_strings: Vec<String> = items
            .iter()
            .filter_map(|i| i.ORIGIN_HI_LIMIT.as_ref().map(|l| l.to_string()))
            .collect();
        let origin_hi_limit_arr = mk_string_distinct(origin_hi_limit_strings.iter().map(|s| s.as_str()).collect());

        let process_arr = mk_string_distinct(items.iter().map(|i| i.PROCESS.as_ref()).collect());
        let testitem_type_arr = mk_string_distinct(items.iter().map(|i| i.TESTITEM_TYPE.as_ref()).collect());
        let test_temperature_arr = mk_string_distinct(items.iter().map(|i| i.TEST_TEMPERATURE.as_ref()).collect());
        let tester_name_arr = mk_string_distinct(items.iter().map(|i| i.TESTER_NAME.as_ref()).collect());
        let tester_type_arr = mk_string_distinct(items.iter().map(|i| i.TESTER_TYPE.as_ref()).collect());
        let prober_handler_id_arr = mk_string_distinct(items.iter().map(|i| i.PROBER_HANDLER_ID.as_ref()).collect());
        let probecard_loadboard_id_arr =
            mk_string_distinct(items.iter().map(|i| i.PROBECARD_LOADBOARD_ID.as_ref()).collect());
        let prober_handler_typ_arr = mk_string_distinct(items.iter().map(|i| i.PROBER_HANDLER_TYP.as_ref()).collect());
        let probecard_loadboard_typ_arr =
            mk_string_distinct(items.iter().map(|i| i.PROBECARD_LOADBOARD_TYP.as_ref()).collect());

        // Site-specific aggregations
        let site_cnt_strings: Vec<String> = items.iter().filter_map(|i| i.SITE_CNT.map(|s| s.to_string())).collect();
        let site_cnt_arr = mk_string_distinct(site_cnt_strings.iter().map(|s| s.as_str()).collect());

        let site_nums_arr = mk_string_distinct(items.iter().map(|i| i.SITE_NUMS.as_ref()).collect());

        // Determine SBLOT_ID and WAFER_NO based on test area (CP support)
        let support_cp_test_areas = ["CP", "CP(Map)", "CP(InklessMap)"];
        let sblot_id = if support_cp_test_areas.contains(&item.TEST_AREA.as_ref()) {
            sblot_id_arr.as_str()
        } else {
            item.SBLOT_ID.as_ref()
        };
        let wafer_no = if support_cp_test_areas.contains(&item.TEST_AREA.as_ref()) {
            item.WAFER_NO.as_ref()
        } else {
            wafer_no_arr.as_str()
        };

        // Calculate counts
        let standard_ecids: HashSet<&str> = items
            .iter()
            .filter(|i| i.IS_STANDARD_ECID == Some(1))
            .map(|i| i.ECID.as_ref())
            .collect();
        let non_standard_ecids: HashSet<&str> = items
            .iter()
            .filter(|i| i.IS_STANDARD_ECID != Some(1))
            .map(|i| i.ECID.as_ref())
            .collect();

        let input_cnt = (standard_ecids.len() + non_standard_ecids.len()) as i32;

        let pass_standard: HashSet<&str> = items
            .iter()
            .filter(|i| i.IS_STANDARD_ECID == Some(1) && i.TEST_RESULT == Some(1))
            .map(|i| i.ECID.as_ref())
            .collect();
        let pass_non_standard: HashSet<&str> = items
            .iter()
            .filter(|i| i.IS_STANDARD_ECID != Some(1) && i.TEST_RESULT == Some(1))
            .map(|i| i.ECID.as_ref())
            .collect();
        let pass_cnt = (pass_standard.len() + pass_non_standard.len()) as i32;

        let fail_standard: HashSet<&str> = items
            .iter()
            .filter(|i| i.IS_STANDARD_ECID == Some(1) && i.TEST_RESULT.is_some() && i.TEST_RESULT.unwrap() != 1)
            .map(|i| i.ECID.as_ref())
            .collect();
        let fail_non_standard: HashSet<&str> = items
            .iter()
            .filter(|i| i.IS_STANDARD_ECID != Some(1) && i.TEST_RESULT.is_some() && i.TEST_RESULT.unwrap() != 1)
            .map(|i| i.ECID.as_ref())
            .collect();
        let fail_cnt = (fail_standard.len() + fail_non_standard.len()) as i32;

        let passbin_failing_item_standard: HashSet<&str> = items
            .iter()
            .filter(|i| i.IS_STANDARD_ECID == Some(1) && i.TEST_RESULT == Some(1) && i.HBIN_PF.as_ref() == PF_PASS)
            .map(|i| i.ECID.as_ref())
            .collect();
        let passbin_failing_item_non_standard: HashSet<&str> = items
            .iter()
            .filter(|i| i.IS_STANDARD_ECID != Some(1) && i.TEST_RESULT == Some(1) && i.HBIN_PF.as_ref() == PF_PASS)
            .map(|i| i.ECID.as_ref())
            .collect();
        let passbin_failingitem_cnt =
            (passbin_failing_item_standard.len() + passbin_failing_item_non_standard.len()) as i32;

        let exe_input_cnt = items.iter().filter(|i| i.IS_STANDARD_ECID == Some(1)).count() as i32
            + items.iter().filter(|i| i.IS_STANDARD_ECID != Some(1)).count() as i32;

        let exe_pass_cnt = items
            .iter()
            .filter(|i| i.IS_STANDARD_ECID == Some(1) && i.TEST_RESULT == Some(1))
            .count() as i32
            + items
                .iter()
                .filter(|i| i.IS_STANDARD_ECID != Some(1) && i.TEST_RESULT == Some(1))
                .count() as i32;

        let exe_fail_cnt = items
            .iter()
            .filter(|i| i.IS_STANDARD_ECID == Some(1) && i.TEST_RESULT.is_some() && i.TEST_RESULT.unwrap() != 1)
            .count() as i32
            + items
                .iter()
                .filter(|i| i.IS_STANDARD_ECID != Some(1) && i.TEST_RESULT.is_some() && i.TEST_RESULT.unwrap() != 1)
                .count() as i32;

        // Calculate time ranges
        let start_times: Vec<i64> = items.iter().filter_map(|i| i.START_TIME).collect();
        let end_times: Vec<i64> = items.iter().filter_map(|i| i.END_TIME).collect();

        let (start_time_min, start_hour_min, start_day_min) = if !start_times.is_empty() {
            let min_time = *start_times.iter().min().unwrap();
            let start_item = items.iter().find(|i| i.START_TIME == Some(min_time)).unwrap();
            (Some(min_time), start_item.START_HOUR_KEY.clone(), start_item.START_DAY_KEY.clone())
        } else {
            (None, Arc::from(""), Arc::from(""))
        };

        let (end_time_max, end_hour_max, end_day_max) = if !end_times.is_empty() {
            let max_time = *end_times.iter().max().unwrap();
            let end_item = items.iter().find(|i| i.END_TIME == Some(max_time)).unwrap();
            (Some(max_time), end_item.END_HOUR_KEY.clone(), end_item.END_DAY_KEY.clone())
        } else {
            (None, Arc::from(""), Arc::from(""))
        };

        // Determine data source based on test area
        let data_source = if item.TEST_AREA.as_ref() == "CP(Map)" {
            CP_MAP
        } else if item.TEST_AREA.as_ref() == "CP(InklessMap)" {
            INKLESS_MAP
        } else {
            TEST_RAW_DATA
        };

        // Check if we should calculate statistical metrics
        if (testitem_type == P || testitem_type == M) && !test_values.is_empty() {
            Self::new_with_statistics(
                data_source,
                item,
                product,
                product_type,
                product_family,
                is_pass_only,
                sblot_id,
                wafer_no,
                wafer_id_arr,
                wafer_lot_id_arr,
                units_arr,
                origin_units_arr,
                lo_limit_arr,
                hi_limit_arr,
                origin_lo_limit_arr,
                origin_hi_limit_arr,
                process_arr,
                testitem_type_arr,
                test_temperature_arr,
                tester_name_arr,
                tester_type_arr,
                prober_handler_typ_arr,
                prober_handler_id_arr,
                probecard_loadboard_typ_arr,
                probecard_loadboard_id_arr,
                site_cnt_arr,
                site_nums_arr,
                input_cnt,
                pass_cnt,
                fail_cnt,
                passbin_failingitem_cnt,
                exe_input_cnt,
                exe_pass_cnt,
                exe_fail_cnt,
                start_time_min,
                start_hour_min,
                start_day_min,
                end_time_max,
                end_hour_max,
                end_day_max,
                create_hour_key,
                create_day_key,
                test_values,
                items,
            )
        } else {
            Self::new_without_statistics(
                data_source,
                item,
                product,
                product_type,
                product_family,
                is_pass_only,
                sblot_id,
                wafer_no,
                wafer_id_arr,
                wafer_lot_id_arr,
                units_arr,
                origin_units_arr,
                lo_limit_arr,
                hi_limit_arr,
                origin_lo_limit_arr,
                origin_hi_limit_arr,
                process_arr,
                testitem_type_arr,
                test_temperature_arr,
                tester_name_arr,
                tester_type_arr,
                prober_handler_typ_arr,
                prober_handler_id_arr,
                probecard_loadboard_typ_arr,
                probecard_loadboard_id_arr,
                site_cnt_arr,
                site_nums_arr,
                input_cnt,
                pass_cnt,
                fail_cnt,
                passbin_failingitem_cnt,
                exe_input_cnt,
                exe_pass_cnt,
                exe_fail_cnt,
                start_time_min,
                start_hour_min,
                start_day_min,
                end_time_max,
                end_hour_max,
                end_day_max,
                create_hour_key,
                create_day_key,
            )
        }
    }

    /// Creates a new TestItemSite with default values
    pub fn new() -> Self {
        let now = chrono::Utc::now();
        Self {
            DATA_SOURCE: Arc::from(""),
            UPLOAD_TYPE: Arc::from(""),
            CUSTOMER: Arc::from(""),
            SUB_CUSTOMER: Arc::from(""),
            FAB: Arc::from(""),
            FAB_SITE: Arc::from(""),
            FACTORY: Arc::from(""),
            FACTORY_SITE: Arc::from(""),
            TEST_AREA: Arc::from(""),
            TEST_STAGE: Arc::from(""),
            DEVICE_ID: Arc::from(""),
            LOT_TYPE: Arc::from(""),
            LOT_ID: Arc::from(""),
            SBLOT_ID: Arc::from(""),
            WAFER_ID: Arc::from(""),
            WAFER_NO: Arc::from(""),
            WAFER_LOT_ID: Arc::from(""),
            PRODUCT: Arc::from(""),
            PRODUCT_TYPE: Arc::from(""),
            PRODUCT_FAMILY: Arc::from(""),
            TEST_PROGRAM: Arc::from(""),
            TEST_PROGRAM_VERSION: Arc::from(""),
            TEST_NUM: None,
            TEST_TXT: Arc::from(""),
            TEST_ITEM: Arc::from(""),
            SITE: None,
            IS_PASS_ONLY: 0,
            IS_FINAL: 0,
            UNITS_LIST: Arc::from(""),
            ORIGIN_UNITS_LIST: Arc::from(""),
            LO_LIMIT_LIST: Arc::from(""),
            HI_LIMIT_LIST: Arc::from(""),
            ORIGIN_LO_LIMIT_LIST: Arc::from(""),
            ORIGIN_HI_LIMIT_LIST: Arc::from(""),
            PROCESS_LIST: Arc::from(""),
            TESTITEM_TYPE_LIST: Arc::from(""),
            TEST_TEMPERATURE_LIST: Arc::from(""),
            TESTER_NAME_LIST: Arc::from(""),
            TESTER_TYPE_LIST: Arc::from(""),
            PROBER_HANDLER_TYP_LIST: Arc::from(""),
            PROBER_HANDLER_ID_LIST: Arc::from(""),
            PROBECARD_LOADBOARD_TYP_LIST: Arc::from(""),
            PROBECARD_LOADBOARD_ID_LIST: Arc::from(""),
            SITE_CNT_LIST: Arc::from(""),
            SITE_NUMS_LIST: Arc::from(""),
            INPUT_CNT: 0,
            PASS_CNT: 0,
            FAIL_CNT: 0,
            PASSBIN_FAILINGITEM_CNT: 0,
            EXE_INPUT_CNT: 0,
            EXE_PASS_CNT: 0,
            EXE_FAIL_CNT: 0,
            MEDIAN: None,
            MEAN: None,
            MAX: None,
            MIN: None,
            MAX_WO_OUTLIERS: None,
            MIN_WO_OUTLIERS: None,
            SUM_SQ: None,
            SUM_VALUE: None,
            STDEV_P: None,
            STDEV_S: None,
            RANGE: None,
            IQR: None,
            Q1: None,
            Q3: None,
            LOWER: None,
            UPPER: None,
            OUTLIER_CNT: 0,
            P1: None,
            P5: None,
            P10: None,
            P90: None,
            P95: None,
            P99: None,
            GROUP_DETAIL: HashMap::new(),
            PP: None,
            PPU: None,
            PPL: None,
            PPK: None,
            CP: None,
            CPU: None,
            CPL: None,
            CPK: None,
            CA: None,
            SKEWNESS: None,
            KURTOSIS: None,
            NORMALIZATION_MEDIAN: None,
            NORMALIZATION_MEAN: None,
            NORMALIZATION_MAX: None,
            NORMALIZATION_MIN: None,
            NORMALIZATION_MAX_WO_OUTLIERS: None,
            NORMALIZATION_MIN_WO_OUTLIERS: None,
            NORMALIZATION_IQR: None,
            NORMALIZATION_Q1: None,
            NORMALIZATION_Q3: None,
            NORMALIZATION_LOWER: None,
            NORMALIZATION_UPPER: None,
            START_TIME: None,
            START_HOUR_KEY: Arc::from(""),
            START_DAY_KEY: Arc::from(""),
            END_TIME: None,
            END_HOUR_KEY: Arc::from(""),
            END_DAY_KEY: Arc::from(""),
            CREATE_TIME: now,
            CREATE_HOUR_KEY: Arc::from(""),
            CREATE_DAY_KEY: Arc::from(""),
            CREATE_USER: Arc::from(""),
            UPLOAD_TIME: now,
            VERSION: 1,
            IS_DELETE: 0,
        }
    }

    /// Creates a new TestItemSite with statistical calculations
    /// Used when testitem_type is P or M and test_values is not empty
    #[allow(clippy::too_many_arguments)]
    fn new_with_statistics(
        data_source: &str,
        item: &crate::dto::ads::value::test_item_detail::TestItemDetail,
        product: &str,
        product_type: &str,
        product_family: &str,
        is_pass_only: u8,
        sblot_id: &str,
        wafer_no: &str,
        wafer_id_arr: String,
        wafer_lot_id_arr: String,
        units_arr: String,
        origin_units_arr: String,
        lo_limit_arr: String,
        hi_limit_arr: String,
        origin_lo_limit_arr: String,
        origin_hi_limit_arr: String,
        process_arr: String,
        testitem_type_arr: String,
        test_temperature_arr: String,
        tester_name_arr: String,
        tester_type_arr: String,
        prober_handler_typ_arr: String,
        prober_handler_id_arr: String,
        probecard_loadboard_typ_arr: String,
        probecard_loadboard_id_arr: String,
        site_cnt_arr: String,
        site_nums_arr: String,
        input_cnt: i32,
        pass_cnt: i32,
        fail_cnt: i32,
        passbin_failingitem_cnt: i32,
        exe_input_cnt: i32,
        exe_pass_cnt: i32,
        exe_fail_cnt: i32,
        start_time_min: Option<i64>,
        start_hour_min: Arc<str>,
        start_day_min: Arc<str>,
        end_time_max: Option<i64>,
        end_hour_max: Arc<str>,
        end_day_max: Arc<str>,
        create_hour_key: String,
        create_day_key: String,
        test_values: Vec<f64>,
        _items: &[crate::dto::ads::value::test_item_detail::TestItemDetail],
    ) -> Self {
        use crate::dto::ads::to_decimal;
        use crate::model::constant::SYSTEM;
        use crate::utils::date::IntoDateTimeUtc;
        use crate::utils::stats::*;

        let now = chrono::Utc::now();

        // Calculate basic statistics
        let median = calculate_median(&test_values).and_then(to_decimal);
        let mean = calculate_mean(&test_values).and_then(to_decimal);
        let max_val = calculate_max(&test_values).and_then(to_decimal);
        let min_val = calculate_min(&test_values).and_then(to_decimal);
        let sum_sq_f64 = calculate_square_sum(&test_values);
        let sum_sq = sum_sq_f64.and_then(to_decimal);
        let sum_value = calculate_sum(&test_values).and_then(to_decimal);
        let stdev_p = calculate_std_dev(&test_values).and_then(to_decimal);
        let stdev_s = if exe_input_cnt > 1 && sum_sq_f64.is_some() {
            to_decimal((sum_sq_f64.unwrap() / (exe_input_cnt - 1) as f64).sqrt())
        } else {
            to_decimal(0.0)
        };
        let range = calculate_range(&test_values).and_then(to_decimal);
        let iqr = calculate_iqr(&test_values).and_then(to_decimal);

        // Calculate quantiles
        let q1 = calculate_q1(&test_values).and_then(to_decimal);
        let q3 = calculate_q3(&test_values).and_then(to_decimal);
        let p1 = quantile1(&test_values).and_then(to_decimal);
        let p5 = quantile5(&test_values).and_then(to_decimal);
        let p10 = quantile10(&test_values).and_then(to_decimal);
        let p90 = quantile90(&test_values).and_then(to_decimal);
        let p95 = quantile95(&test_values).and_then(to_decimal);
        let p99 = quantile99(&test_values).and_then(to_decimal);

        // Calculate outlier bounds and filter outliers
        let (lower_bound, upper_bound, outlier_cnt, filtered_values) =
            if let Some((lower, upper)) = calculate_outlier_bounds(&test_values) {
                let outliers = detect_outliers(&test_values);
                let filtered = filter_outliers(&test_values);
                (to_decimal(lower), to_decimal(upper), outliers.len() as u32, filtered)
            } else {
                (None, None, 0, test_values.clone())
            };

        // Calculate statistics without outliers
        let max_wo_outliers =
            if !filtered_values.is_empty() { calculate_max(&filtered_values).and_then(to_decimal) } else { None };
        let min_wo_outliers =
            if !filtered_values.is_empty() { calculate_min(&filtered_values).and_then(to_decimal) } else { None };

        // Calculate process capability indices
        let (cp, cpu, cpl, cpk, ca) = if let (Some(mean_val), Some(std_dev_val)) = (mean, stdev_p) {
            let mean_f64 = mean_val.to_string().parse::<f64>().unwrap_or(0.0);
            let std_dev_f64 = std_dev_val.to_string().parse::<f64>().unwrap_or(0.0);

            // Get limits from the first item
            let lo_limit = item.LO_LIMIT.map(|l| l.to_string().parse::<f64>().unwrap_or(0.0));
            let hi_limit = item.HI_LIMIT.map(|l| l.to_string().parse::<f64>().unwrap_or(0.0));

            let cp_val = if let (Some(lo), Some(hi)) = (lo_limit, hi_limit) {
                to_decimal(calculate_cp(std_dev_f64, lo, hi))
            } else {
                None
            };

            let cpu_val =
                if let Some(hi) = hi_limit { to_decimal(calculate_cpu(mean_f64, std_dev_f64, hi)) } else { None };

            let cpl_val =
                if let Some(lo) = lo_limit { to_decimal(calculate_cpl(mean_f64, std_dev_f64, lo)) } else { None };

            let cpk_val = if let (Some(lo), Some(hi)) = (lo_limit, hi_limit) {
                to_decimal(calculate_cpk(mean_f64, std_dev_f64, lo, hi))
            } else {
                None
            };

            let ca_val = if let (Some(lo), Some(hi)) = (lo_limit, hi_limit) {
                if hi != lo {
                    to_decimal(calculate_ca(mean_f64, lo, hi))
                } else {
                    None
                }
            } else {
                None
            };

            (cp_val, cpu_val, cpl_val, cpk_val, ca_val)
        } else {
            (None, None, None, None, None)
        };

        // PP values are same as CP values for this implementation
        let pp = cp;
        let ppu = cpu;
        let ppl = cpl;
        let ppk = cpk;

        // Calculate advanced statistics
        let skewness = calculate_skewness(&test_values).and_then(to_decimal);
        let kurtosis = calculate_kurtosis(&test_values).and_then(to_decimal);

        Self {
            DATA_SOURCE: Arc::from(data_source),
            UPLOAD_TYPE: item.UPLOAD_TYPE.clone(),
            CUSTOMER: item.CUSTOMER.clone(),
            SUB_CUSTOMER: item.SUB_CUSTOMER.clone(),
            FAB: item.FAB.clone(),
            FAB_SITE: item.FAB_SITE.clone(),
            FACTORY: item.FACTORY.clone(),
            FACTORY_SITE: item.FACTORY_SITE.clone(),
            TEST_AREA: item.TEST_AREA.clone(),
            TEST_STAGE: item.TEST_STAGE.clone(),
            DEVICE_ID: item.DEVICE_ID.clone(),
            LOT_TYPE: item.LOT_TYPE.clone(),
            LOT_ID: item.LOT_ID.clone(),
            SBLOT_ID: Arc::from(sblot_id),
            WAFER_ID: Arc::from(wafer_id_arr),
            WAFER_NO: Arc::from(wafer_no),
            WAFER_LOT_ID: Arc::from(wafer_lot_id_arr),
            PRODUCT: Arc::from(product),
            PRODUCT_TYPE: Arc::from(product_type),
            PRODUCT_FAMILY: Arc::from(product_family),
            TEST_PROGRAM: item.TEST_PROGRAM.clone(),
            TEST_PROGRAM_VERSION: item.TEST_PROGRAM_VERSION.clone(),
            TEST_NUM: item.TEST_NUM,
            TEST_TXT: item.TEST_TXT.clone(),
            TEST_ITEM: item.TEST_ITEM.clone(),
            SITE: item.SITE,
            IS_PASS_ONLY: is_pass_only,
            IS_FINAL: 0,
            UNITS_LIST: Arc::from(units_arr),
            ORIGIN_UNITS_LIST: Arc::from(origin_units_arr),
            LO_LIMIT_LIST: Arc::from(lo_limit_arr),
            HI_LIMIT_LIST: Arc::from(hi_limit_arr),
            ORIGIN_LO_LIMIT_LIST: Arc::from(origin_lo_limit_arr),
            ORIGIN_HI_LIMIT_LIST: Arc::from(origin_hi_limit_arr),
            PROCESS_LIST: Arc::from(process_arr),
            TESTITEM_TYPE_LIST: Arc::from(testitem_type_arr),
            TEST_TEMPERATURE_LIST: Arc::from(test_temperature_arr),
            TESTER_NAME_LIST: Arc::from(tester_name_arr),
            TESTER_TYPE_LIST: Arc::from(tester_type_arr),
            PROBER_HANDLER_TYP_LIST: Arc::from(prober_handler_typ_arr),
            PROBER_HANDLER_ID_LIST: Arc::from(prober_handler_id_arr),
            PROBECARD_LOADBOARD_TYP_LIST: Arc::from(probecard_loadboard_typ_arr),
            PROBECARD_LOADBOARD_ID_LIST: Arc::from(probecard_loadboard_id_arr),
            SITE_CNT_LIST: Arc::from(site_cnt_arr),
            SITE_NUMS_LIST: Arc::from(site_nums_arr),
            INPUT_CNT: input_cnt,
            PASS_CNT: pass_cnt,
            FAIL_CNT: fail_cnt,
            PASSBIN_FAILINGITEM_CNT: passbin_failingitem_cnt,
            EXE_INPUT_CNT: exe_input_cnt,
            EXE_PASS_CNT: exe_pass_cnt,
            EXE_FAIL_CNT: exe_fail_cnt,
            MEDIAN: median,
            MEAN: mean,
            MAX: max_val,
            MIN: min_val,
            MAX_WO_OUTLIERS: max_wo_outliers,
            MIN_WO_OUTLIERS: min_wo_outliers,
            SUM_SQ: sum_sq,
            SUM_VALUE: sum_value,
            STDEV_P: stdev_p,
            STDEV_S: stdev_s,
            RANGE: range,
            IQR: iqr,
            Q1: q1,
            Q3: q3,
            LOWER: lower_bound,
            UPPER: upper_bound,
            OUTLIER_CNT: outlier_cnt,
            P1: p1,
            P5: p5,
            P10: p10,
            P90: p90,
            P95: p95,
            P99: p99,
            GROUP_DETAIL: Self::create_histogram(&test_values),
            PP: pp,
            PPU: ppu,
            PPL: ppl,
            PPK: ppk,
            CP: cp,
            CPU: cpu,
            CPL: cpl,
            CPK: cpk,
            CA: ca,
            SKEWNESS: skewness,
            KURTOSIS: kurtosis,
            NORMALIZATION_MEDIAN: median,
            NORMALIZATION_MEAN: mean,
            NORMALIZATION_MAX: max_val,
            NORMALIZATION_MIN: min_val,
            NORMALIZATION_MAX_WO_OUTLIERS: max_wo_outliers,
            NORMALIZATION_MIN_WO_OUTLIERS: min_wo_outliers,
            NORMALIZATION_IQR: iqr,
            NORMALIZATION_Q1: q1,
            NORMALIZATION_Q3: q3,
            NORMALIZATION_LOWER: lower_bound,
            NORMALIZATION_UPPER: upper_bound,
            START_TIME: start_time_min.map(|t| t.into_utc()),
            START_HOUR_KEY: start_hour_min,
            START_DAY_KEY: start_day_min,
            END_TIME: end_time_max.map(|t| t.into_utc()),
            END_HOUR_KEY: end_hour_max,
            END_DAY_KEY: end_day_max,
            CREATE_TIME: now,
            CREATE_HOUR_KEY: Arc::from(create_hour_key),
            CREATE_DAY_KEY: Arc::from(create_day_key),
            CREATE_USER: Arc::from(SYSTEM),
            UPLOAD_TIME: now,
            VERSION: 1,
            IS_DELETE: 0,
        }
    }

    /// Creates a new TestItemSite without statistical calculations
    /// Used when testitem_type is not P or M, or when test_values is empty
    #[allow(clippy::too_many_arguments)]
    fn new_without_statistics(
        data_source: &str,
        item: &crate::dto::ads::value::test_item_detail::TestItemDetail,
        product: &str,
        product_type: &str,
        product_family: &str,
        is_pass_only: u8,
        sblot_id: &str,
        wafer_no: &str,
        wafer_id_arr: String,
        wafer_lot_id_arr: String,
        units_arr: String,
        origin_units_arr: String,
        lo_limit_arr: String,
        hi_limit_arr: String,
        origin_lo_limit_arr: String,
        origin_hi_limit_arr: String,
        process_arr: String,
        testitem_type_arr: String,
        test_temperature_arr: String,
        tester_name_arr: String,
        tester_type_arr: String,
        prober_handler_typ_arr: String,
        prober_handler_id_arr: String,
        probecard_loadboard_typ_arr: String,
        probecard_loadboard_id_arr: String,
        site_cnt_arr: String,
        site_nums_arr: String,
        input_cnt: i32,
        pass_cnt: i32,
        fail_cnt: i32,
        passbin_failingitem_cnt: i32,
        exe_input_cnt: i32,
        exe_pass_cnt: i32,
        exe_fail_cnt: i32,
        start_time_min: Option<i64>,
        start_hour_min: Arc<str>,
        start_day_min: Arc<str>,
        end_time_max: Option<i64>,
        end_hour_max: Arc<str>,
        end_day_max: Arc<str>,
        create_hour_key: String,
        create_day_key: String,
    ) -> Self {
        use crate::model::constant::SYSTEM;
        use crate::utils::date::IntoDateTimeUtc;

        let now = chrono::Utc::now();

        Self {
            DATA_SOURCE: Arc::from(data_source),
            UPLOAD_TYPE: item.UPLOAD_TYPE.clone(),
            CUSTOMER: item.CUSTOMER.clone(),
            SUB_CUSTOMER: item.SUB_CUSTOMER.clone(),
            FAB: item.FAB.clone(),
            FAB_SITE: item.FAB_SITE.clone(),
            FACTORY: item.FACTORY.clone(),
            FACTORY_SITE: item.FACTORY_SITE.clone(),
            TEST_AREA: item.TEST_AREA.clone(),
            TEST_STAGE: item.TEST_STAGE.clone(),
            DEVICE_ID: item.DEVICE_ID.clone(),
            LOT_TYPE: item.LOT_TYPE.clone(),
            LOT_ID: item.LOT_ID.clone(),
            SBLOT_ID: Arc::from(sblot_id),
            WAFER_ID: Arc::from(wafer_id_arr),
            WAFER_NO: Arc::from(wafer_no),
            WAFER_LOT_ID: Arc::from(wafer_lot_id_arr),
            PRODUCT: Arc::from(product),
            PRODUCT_TYPE: Arc::from(product_type),
            PRODUCT_FAMILY: Arc::from(product_family),
            TEST_PROGRAM: item.TEST_PROGRAM.clone(),
            TEST_PROGRAM_VERSION: item.TEST_PROGRAM_VERSION.clone(),
            TEST_NUM: item.TEST_NUM,
            TEST_TXT: item.TEST_TXT.clone(),
            TEST_ITEM: item.TEST_ITEM.clone(),
            SITE: item.SITE,
            IS_PASS_ONLY: is_pass_only,
            IS_FINAL: 0,
            UNITS_LIST: Arc::from(units_arr),
            ORIGIN_UNITS_LIST: Arc::from(origin_units_arr),
            LO_LIMIT_LIST: Arc::from(lo_limit_arr),
            HI_LIMIT_LIST: Arc::from(hi_limit_arr),
            ORIGIN_LO_LIMIT_LIST: Arc::from(origin_lo_limit_arr),
            ORIGIN_HI_LIMIT_LIST: Arc::from(origin_hi_limit_arr),
            PROCESS_LIST: Arc::from(process_arr),
            TESTITEM_TYPE_LIST: Arc::from(testitem_type_arr),
            TEST_TEMPERATURE_LIST: Arc::from(test_temperature_arr),
            TESTER_NAME_LIST: Arc::from(tester_name_arr),
            TESTER_TYPE_LIST: Arc::from(tester_type_arr),
            PROBER_HANDLER_TYP_LIST: Arc::from(prober_handler_typ_arr),
            PROBER_HANDLER_ID_LIST: Arc::from(prober_handler_id_arr),
            PROBECARD_LOADBOARD_TYP_LIST: Arc::from(probecard_loadboard_typ_arr),
            PROBECARD_LOADBOARD_ID_LIST: Arc::from(probecard_loadboard_id_arr),
            SITE_CNT_LIST: Arc::from(site_cnt_arr),
            SITE_NUMS_LIST: Arc::from(site_nums_arr),
            INPUT_CNT: input_cnt,
            PASS_CNT: pass_cnt,
            FAIL_CNT: fail_cnt,
            PASSBIN_FAILINGITEM_CNT: passbin_failingitem_cnt,
            EXE_INPUT_CNT: exe_input_cnt,
            EXE_PASS_CNT: exe_pass_cnt,
            EXE_FAIL_CNT: exe_fail_cnt,
            // All statistical fields are None for non-statistical types
            MEDIAN: None,
            MEAN: None,
            MAX: None,
            MIN: None,
            MAX_WO_OUTLIERS: None,
            MIN_WO_OUTLIERS: None,
            SUM_SQ: None,
            SUM_VALUE: None,
            STDEV_P: None,
            STDEV_S: None,
            RANGE: None,
            IQR: None,
            Q1: None,
            Q3: None,
            LOWER: None,
            UPPER: None,
            OUTLIER_CNT: 0,
            P1: None,
            P5: None,
            P10: None,
            P90: None,
            P95: None,
            P99: None,
            GROUP_DETAIL: HashMap::new(),
            PP: None,
            PPU: None,
            PPL: None,
            PPK: None,
            CP: None,
            CPU: None,
            CPL: None,
            CPK: None,
            CA: None,
            SKEWNESS: None,
            KURTOSIS: None,
            NORMALIZATION_MEDIAN: None,
            NORMALIZATION_MEAN: None,
            NORMALIZATION_MAX: None,
            NORMALIZATION_MIN: None,
            NORMALIZATION_MAX_WO_OUTLIERS: None,
            NORMALIZATION_MIN_WO_OUTLIERS: None,
            NORMALIZATION_IQR: None,
            NORMALIZATION_Q1: None,
            NORMALIZATION_Q3: None,
            NORMALIZATION_LOWER: None,
            NORMALIZATION_UPPER: None,
            START_TIME: start_time_min.map(|t| t.into_utc()),
            START_HOUR_KEY: start_hour_min,
            START_DAY_KEY: start_day_min,
            END_TIME: end_time_max.map(|t| t.into_utc()),
            END_HOUR_KEY: end_hour_max,
            END_DAY_KEY: end_day_max,
            CREATE_TIME: now,
            CREATE_HOUR_KEY: Arc::from(create_hour_key),
            CREATE_DAY_KEY: Arc::from(create_day_key),
            CREATE_USER: Arc::from(SYSTEM),
            UPLOAD_TIME: now,
            VERSION: 1,
            IS_DELETE: 0,
        }
    }

    /// Creates a histogram from test values
    /// Divides the range into 100 buckets and counts values in each bucket
    fn create_histogram(test_values: &[f64]) -> HashMap<String, u32> {
        use crate::model::constant::ZERO;

        if test_values.is_empty() {
            return HashMap::new();
        }

        let min_val = test_values.iter().fold(f64::INFINITY, |a, &b| a.min(b));
        let max_val = test_values.iter().fold(f64::NEG_INFINITY, |a, &b| a.max(b));
        let range = max_val - min_val;

        let mut histogram = HashMap::new();

        if range == 0.0 {
            // All values are the same
            histogram.insert(ZERO.to_string(), test_values.len() as u32);
        } else {
            // Create 100 buckets
            for &value in test_values {
                let bucket_index = ((value - min_val) / (range / 100.0)).floor() as i32;
                let bucket_key =
                    if bucket_index >= 100 || bucket_index < 0 { ZERO.to_string() } else { bucket_index.to_string() };
                *histogram.entry(bucket_key).or_insert(0) += 1;
            }
        }

        histogram
    }
}

impl Default for TestItemSite {
    fn default() -> Self {
        Self::new()
    }
}
