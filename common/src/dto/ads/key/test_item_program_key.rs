use serde::{Deserialize, Serialize};
use std::hash::{Hash, Hasher};

/// Aggregation key for program-level test item grouping
/// Groups test items by customer, device, lot, test program, and test item details
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub struct TestItemProgramKey {
    pub customer: String,
    pub device_id: String,
    pub lot_id: String,
    pub test_program: String,
    pub test_num: i64,
    pub test_txt: String,
    pub test_item: String,
}

impl Hash for TestItemProgramKey {
    fn hash<H: Hasher>(&self, state: &mut H) {
        self.customer.hash(state);
        self.device_id.hash(state);
        self.lot_id.hash(state);
        self.test_program.hash(state);
        self.test_num.hash(state);
        self.test_txt.hash(state);
        self.test_item.hash(state);
    }
}

impl TestItemProgramKey {
    /// Creates a new TestItemProgramKey
    pub fn new(
        customer: String,
        device_id: String,
        lot_id: String,
        test_program: String,
        test_num: i64,
        test_txt: String,
        test_item: String,
    ) -> Self {
        Self {
            customer,
            device_id,
            lot_id,
            test_program,
            test_num,
            test_txt,
            test_item,
        }
    }

    /// Creates a TestItemProgramKey from a TestItemDetail
    pub fn from_test_item_detail(detail: &crate::dto::ads::value::test_item_detail::TestItemDetail) -> Self {
        Self {
            customer: detail.CUSTOMER.to_string(),
            device_id: detail.DEVICE_ID.to_string(),
            lot_id: detail.LOT_ID.to_string(),
            test_program: detail.TEST_PROGRAM.to_string(),
            test_num: detail.TEST_NUM.unwrap_or(0) as i64,
            test_txt: detail.TEST_TXT.to_string(),
            test_item: detail.TEST_ITEM.to_string(),
        }
    }
}
